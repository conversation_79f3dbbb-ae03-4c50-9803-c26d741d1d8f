#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Graph_v4 依赖检查脚本
检查和安装 AI小说管理工具 所需的依赖包
"""

import sys
import subprocess
import importlib
from typing import List, Tuple, Dict

# Graph_v4 所需的依赖包
REQUIRED_PACKAGES = [
    # 核心依赖（从代码分析得出）
    ("sqlite3", "sqlite3", "内置模块", True),  # 数据库
    ("json", "json", "内置模块", True),        # JSON处理
    ("logging", "logging", "内置模块", True),  # 日志
    ("datetime", "datetime", "内置模块", True), # 时间处理
    ("pathlib", "pathlib", "内置模块", True),  # 路径处理
    ("uuid", "uuid", "内置模块", True),        # UUID生成
    ("dataclasses", "dataclasses", "内置模块", True), # 数据类
    ("enum", "enum", "内置模块", True),        # 枚举
    ("typing", "typing", "内置模块", True),    # 类型提示
    ("contextlib", "contextlib", "内置模块", True), # 上下文管理
    ("collections", "collections", "内置模块", True), # 集合类型
]

# 可选依赖（用于高级功能）
OPTIONAL_PACKAGES = [
    # 网络分析（从v2/v3版本推断）
    ("networkx", "networkx", "pip install networkx", False),
    # 数据处理
    ("pandas", "pandas", "pip install pandas", False),
    # 数值计算
    ("numpy", "numpy", "pip install numpy", False),
    # 可视化（可选）
    ("matplotlib", "matplotlib", "pip install matplotlib", False),
    ("plotly", "plotly", "pip install plotly", False),
]

def check_package(package_name: str, import_name: str) -> Tuple[bool, str]:
    """检查单个包是否可用"""
    try:
        importlib.import_module(import_name)
        return True, "已安装"
    except ImportError:
        return False, "未安装"
    except Exception as e:
        return False, f"错误: {str(e)}"

def install_package(package_name: str) -> bool:
    """安装包"""
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", package_name
        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        return True
    except subprocess.CalledProcessError:
        return False

def check_python_version() -> bool:
    """检查Python版本"""
    version = sys.version_info
    if version.major >= 3 and version.minor >= 7:
        return True
    return False

def main():
    """主检查函数"""
    print("=" * 60)
    print("🔍 Graph_v4 依赖检查")
    print("=" * 60)
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    if not check_python_version():
        print("❌ Python版本过低，需要Python 3.7+")
        return False
    else:
        print("✅ Python版本符合要求")
    
    print("\n📦 检查必需依赖...")
    required_missing = []
    
    for package_name, import_name, install_cmd, is_builtin in REQUIRED_PACKAGES:
        available, status = check_package(package_name, import_name)
        status_icon = "✅" if available else "❌"
        print(f"  {status_icon} {package_name:15} : {status}")
        
        if not available and not is_builtin:
            required_missing.append((package_name, install_cmd))
    
    print("\n📦 检查可选依赖...")
    optional_missing = []
    optional_available = []
    
    for package_name, import_name, install_cmd, is_builtin in OPTIONAL_PACKAGES:
        available, status = check_package(package_name, import_name)
        status_icon = "✅" if available else "⚠️ "
        print(f"  {status_icon} {package_name:15} : {status}")
        
        if available:
            optional_available.append(package_name)
        else:
            optional_missing.append((package_name, install_cmd))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 依赖检查结果")
    print("=" * 60)
    
    if required_missing:
        print("❌ 缺少必需依赖:")
        for package, cmd in required_missing:
            print(f"   - {package}: {cmd}")
        print("\n💡 请安装缺少的必需依赖后再运行系统")
        return False
    else:
        print("✅ 所有必需依赖都已满足")
    
    if optional_missing:
        print(f"\n⚠️  缺少 {len(optional_missing)} 个可选依赖:")
        for package, cmd in optional_missing:
            print(f"   - {package}: {cmd}")
        print("\n💡 可选依赖用于高级功能，不影响基本使用")
    
    if optional_available:
        print(f"\n✅ 已安装 {len(optional_available)} 个可选依赖:")
        for package in optional_available:
            print(f"   - {package}")
    
    # 提供安装建议
    print("\n" + "=" * 60)
    print("💡 安装建议")
    print("=" * 60)
    
    if optional_missing:
        print("如需完整功能，建议安装以下可选依赖:")
        print("```bash")
        for package, cmd in optional_missing:
            if cmd.startswith("pip install"):
                print(cmd)
        print("```")
        
        print("\n或一次性安装所有可选依赖:")
        print("```bash")
        packages = [pkg for pkg, _ in optional_missing]
        if packages:
            print(f"pip install {' '.join(packages)}")
        print("```")
    
    print("\n🚀 系统可行性评估:")
    if not required_missing:
        if len(optional_available) >= 2:
            print("✅ 系统完全可用，支持所有功能")
        elif len(optional_available) >= 1:
            print("✅ 系统基本可用，支持大部分功能")
        else:
            print("✅ 系统基础可用，建议安装可选依赖以获得更好体验")
        return True
    else:
        print("❌ 系统不可用，请先安装必需依赖")
        return False

if __name__ == "__main__":
    try:
        success = main()
        print("\n" + "=" * 60)
        if success:
            print("🎉 依赖检查完成，可以运行 test_graph_v4_feasibility.py 进行功能测试")
        else:
            print("⚠️  请解决依赖问题后再进行测试")
        print("=" * 60)
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  检查被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 检查过程中发生错误: {e}")
        sys.exit(1)
