"""
CharacterKnowledgeBase - 智能人物知识库
支持版本化的角色发展轨迹和AI友好的数据结构
"""

import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import numpy as np
from novel_v4_system import BaseModule, ModuleStatus, generate_id, now_iso, logger


class CharacterArchetype(Enum):
    """角色原型枚举"""
    HERO = "英雄"
    MENTOR = "导师"
    THRESHOLD_GUARDIAN = "门槛守护者"
    HERALD = "传令者"
    SHAPESHIFTER = "变形者"
    SHADOW = "阴影"
    ALLY = "盟友"
    TRICKSTER = "小丑"
    INNOCENT = "天真者"
    EXPLORER = "探险家"
    SAGE = "智者"
    OUTLAW = "叛逆者"
    MAGICIAN = "魔法师"
    EVERYMAN = "凡人"
    LOVER = "恋人"
    JESTER = "弄臣"
    CAREGIVER = "照顾者"
    CREATOR = "创造者"
    RULER = "统治者"


class NarrativeRole(Enum):
    """叙事角色枚举"""
    PROTAGONIST = "主角"
    ANTAGONIST = "反派"
    DEUTERAGONIST = "第二主角"
    TRITAGONIST = "第三主角"
    SUPPORTING = "配角"
    MINOR = "次要角色"
    CAMEO = "客串"


@dataclass
class PersonalityVector:
    """人格向量 - 用于AI分析的数值化表示"""
    openness: float = 0.5          # 开放性 (0-1)
    conscientiousness: float = 0.5  # 尽责性 (0-1)
    extraversion: float = 0.5       # 外向性 (0-1)
    agreeableness: float = 0.5      # 宜人性 (0-1)
    neuroticism: float = 0.5        # 神经质 (0-1)
    
    # 扩展维度
    courage: float = 0.5            # 勇气 (0-1)
    intelligence: float = 0.5       # 智力 (0-1)
    morality: float = 0.5          # 道德感 (0-1)
    ambition: float = 0.5          # 野心 (0-1)
    empathy: float = 0.5           # 共情能力 (0-1)
    
    def to_vector(self) -> List[float]:
        """转换为向量形式"""
        return [
            self.openness, self.conscientiousness, self.extraversion,
            self.agreeableness, self.neuroticism, self.courage,
            self.intelligence, self.morality, self.ambition, self.empathy
        ]
    
    def similarity(self, other: 'PersonalityVector') -> float:
        """计算与另一个人格向量的相似度"""
        v1 = np.array(self.to_vector())
        v2 = np.array(other.to_vector())
        return float(np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2)))


@dataclass
class CharacterChange:
    """角色变化记录"""
    timestamp: str
    change_type: str  # "attribute", "relationship", "status", "growth"
    attribute_name: str
    old_value: Any
    new_value: Any
    trigger_event: Optional[str] = None
    description: str = ""
    confidence: float = 1.0  # 变化的确信度 (0-1)
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = now_iso()


@dataclass
class GrowthPattern:
    """成长模式分析"""
    pattern_type: str  # "linear", "exponential", "plateau", "decline", "cyclical"
    growth_rate: float  # 成长速度
    stability: float    # 稳定性
    key_factors: List[str]  # 关键影响因素
    predicted_trajectory: str  # 预测轨迹描述


class CharacterKnowledgeBase(BaseModule):
    """智能人物知识库"""
    
    def __init__(self):
        super().__init__("CharacterKnowledgeBase")
        
        # 核心数据结构
        self.characters = {}  # character_id -> character_data
        self.name_to_id = {}  # name -> character_id (支持重名处理)
        self.development_history = {}  # character_id -> List[CharacterChange]
        self.growth_analysis = {}  # character_id -> GrowthPattern
        
        # 索引
        self.archetype_index = {}  # archetype -> List[character_id]
        self.role_index = {}       # narrative_role -> List[character_id]
        self.tag_index = {}        # tag -> List[character_id]
        
        self.status = ModuleStatus.READY
        logger.info("CharacterKnowledgeBase 初始化完成")
    
    def validate_data(self, data: Dict[str, Any]) -> bool:
        """验证角色数据格式"""
        required_fields = ["name"]
        for field in required_fields:
            if field not in data:
                logger.error(f"缺少必需字段: {field}")
                return False
        return True
    
    def _ensure_unique_name(self, name: str) -> str:
        """确保名字唯一性"""
        if name not in self.name_to_id:
            return name
        
        counter = 2
        while f"{name}#{counter}" in self.name_to_id:
            counter += 1
        return f"{name}#{counter}"
    
    def _create_personality_vector(self, character_data: Dict[str, Any]) -> PersonalityVector:
        """从角色数据创建人格向量"""
        personality_data = character_data.get('personality_traits', {})
        
        return PersonalityVector(
            openness=personality_data.get('openness', 0.5),
            conscientiousness=personality_data.get('conscientiousness', 0.5),
            extraversion=personality_data.get('extraversion', 0.5),
            agreeableness=personality_data.get('agreeableness', 0.5),
            neuroticism=personality_data.get('neuroticism', 0.5),
            courage=personality_data.get('courage', 0.5),
            intelligence=personality_data.get('intelligence', 0.5),
            morality=personality_data.get('morality', 0.5),
            ambition=personality_data.get('ambition', 0.5),
            empathy=personality_data.get('empathy', 0.5)
        )
    
    def add_item(self, item_data: Dict[str, Any]) -> str:
        """添加角色"""
        return self.add_character(item_data)
    
    def add_character(self, character_data: Dict[str, Any]) -> str:
        """添加角色到知识库"""
        if not self.validate_data(character_data):
            return ""
        
        # 生成唯一ID和名字
        character_id = generate_id("char")
        unique_name = self._ensure_unique_name(character_data["name"])
        
        # 创建人格向量
        personality_vector = self._create_personality_vector(character_data)
        
        # 构建完整的角色数据
        full_character_data = {
            "id": character_id,
            "name": unique_name,
            "original_name": character_data["name"],
            
            # 基础信息
            "archetype": character_data.get("archetype", ""),
            "narrative_role": character_data.get("narrative_role", ""),
            "core_identity": character_data.get("core_identity", {}),
            "internal_dimension": character_data.get("internal_dimension", {}),
            "external_dimension": character_data.get("external_dimension", {}),
            "tags": character_data.get("tags", []),
            
            # AI分析数据
            "personality_vector": asdict(personality_vector),
            "ai_analysis": {
                "personality_summary": "",
                "growth_potential": 0.5,
                "conflict_potential": {},
                "relationship_compatibility": {},
                "narrative_importance": 0.5
            },
            
            # 元数据
            "created_time": now_iso(),
            "updated_time": now_iso(),
            "version": 1
        }
        
        # 存储数据
        self.characters[character_id] = full_character_data
        self.name_to_id[unique_name] = character_id
        self.development_history[character_id] = []
        
        # 更新索引
        self._update_indexes(character_id, full_character_data)
        
        # 记录创建事件
        self._record_change(character_id, CharacterChange(
            timestamp=now_iso(),
            change_type="creation",
            attribute_name="character",
            old_value=None,
            new_value=unique_name,
            description=f"创建角色: {unique_name}"
        ))
        
        self._touch()
        logger.info(f"添加角色: {unique_name} (ID: {character_id})")
        return character_id
    
    def _update_indexes(self, character_id: str, character_data: Dict[str, Any]):
        """更新索引"""
        # 原型索引
        archetype = character_data.get("archetype")
        if archetype:
            if archetype not in self.archetype_index:
                self.archetype_index[archetype] = []
            if character_id not in self.archetype_index[archetype]:
                self.archetype_index[archetype].append(character_id)
        
        # 角色索引
        role = character_data.get("narrative_role")
        if role:
            if role not in self.role_index:
                self.role_index[role] = []
            if character_id not in self.role_index[role]:
                self.role_index[role].append(character_id)
        
        # 标签索引
        tags = character_data.get("tags", [])
        for tag in tags:
            if tag not in self.tag_index:
                self.tag_index[tag] = []
            if character_id not in self.tag_index[tag]:
                self.tag_index[tag].append(character_id)
    
    def _record_change(self, character_id: str, change: CharacterChange):
        """记录角色变化"""
        if character_id not in self.development_history:
            self.development_history[character_id] = []
        self.development_history[character_id].append(change)
    
    def get_item(self, item_id: str) -> Optional[Dict[str, Any]]:
        """获取角色"""
        return self.get_character(item_id)
    
    def get_character(self, character_id: str) -> Optional[Dict[str, Any]]:
        """获取角色信息"""
        return self.characters.get(character_id)
    
    def get_character_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """通过名字获取角色"""
        character_id = self.name_to_id.get(name)
        if character_id:
            return self.characters.get(character_id)
        return None
    
    def update_item(self, item_id: str, updates: Dict[str, Any]) -> bool:
        """更新角色"""
        return self.update_character(item_id, updates)
    
    def update_character(self, character_id: str, updates: Dict[str, Any]) -> bool:
        """更新角色信息"""
        if character_id not in self.characters:
            logger.error(f"角色不存在: {character_id}")
            return False
        
        character = self.characters[character_id]
        old_version = character.copy()
        
        # 记录变化
        for key, new_value in updates.items():
            if key in character:
                old_value = character[key]
                if old_value != new_value:
                    self._record_change(character_id, CharacterChange(
                        timestamp=now_iso(),
                        change_type="attribute",
                        attribute_name=key,
                        old_value=old_value,
                        new_value=new_value,
                        description=f"更新 {key}: {old_value} -> {new_value}"
                    ))
        
        # 应用更新
        character.update(updates)
        character["updated_time"] = now_iso()
        character["version"] = character.get("version", 1) + 1
        
        # 更新索引
        self._update_indexes(character_id, character)
        
        self._touch()
        logger.info(f"更新角色: {character['name']} (ID: {character_id})")
        return True
    
    def delete_item(self, item_id: str) -> bool:
        """删除角色"""
        return self.delete_character(item_id)
    
    def delete_character(self, character_id: str) -> bool:
        """删除角色"""
        if character_id not in self.characters:
            logger.error(f"角色不存在: {character_id}")
            return False
        
        character = self.characters[character_id]
        name = character["name"]
        
        # 从主数据中删除
        del self.characters[character_id]
        del self.name_to_id[name]
        
        # 从历史记录中删除
        if character_id in self.development_history:
            del self.development_history[character_id]
        
        # 从成长分析中删除
        if character_id in self.growth_analysis:
            del self.growth_analysis[character_id]
        
        # 从索引中删除
        self._remove_from_indexes(character_id)
        
        self._touch()
        logger.info(f"删除角色: {name} (ID: {character_id})")
        return True
    
    def _remove_from_indexes(self, character_id: str):
        """从索引中移除角色"""
        for index in [self.archetype_index, self.role_index, self.tag_index]:
            for key, char_list in index.items():
                if character_id in char_list:
                    char_list.remove(character_id)
    
    def search_items(self, criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """搜索角色"""
        return self.search_characters(criteria)
    
    def search_characters(self, criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """搜索角色"""
        results = []
        
        for character_id, character in self.characters.items():
            match = True
            
            # 检查各种条件
            for key, value in criteria.items():
                if key == "name" and value.lower() not in character.get("name", "").lower():
                    match = False
                    break
                elif key == "archetype" and character.get("archetype") != value:
                    match = False
                    break
                elif key == "narrative_role" and character.get("narrative_role") != value:
                    match = False
                    break
                elif key == "tags" and not any(tag in character.get("tags", []) for tag in value):
                    match = False
                    break
            
            if match:
                results.append(character)
        
        return results

    def get_character_development_history(self, character_id: str) -> List[Dict[str, Any]]:
        """获取角色发展历史"""
        if character_id not in self.development_history:
            return []

        return [asdict(change) for change in self.development_history[character_id]]

    def analyze_character_growth(self, character_id: str) -> Optional[Dict[str, Any]]:
        """分析角色成长模式"""
        if character_id not in self.characters:
            return None

        history = self.development_history.get(character_id, [])
        if len(history) < 2:
            return {"pattern": "insufficient_data", "message": "需要更多发展历史数据"}

        # 简单的成长分析
        growth_events = [change for change in history if change.change_type == "growth"]
        attribute_changes = [change for change in history if change.change_type == "attribute"]

        analysis = {
            "total_changes": len(history),
            "growth_events": len(growth_events),
            "attribute_changes": len(attribute_changes),
            "growth_rate": len(growth_events) / max(1, len(history)),
            "stability": 1.0 - (len(attribute_changes) / max(1, len(history))),
            "key_changes": [asdict(change) for change in history[-5:]],  # 最近5次变化
            "pattern_type": "developing" if len(growth_events) > len(attribute_changes) else "stable"
        }

        return analysis

    def find_similar_characters(self, character_id: str, limit: int = 5) -> List[Tuple[str, float]]:
        """找到相似的角色"""
        if character_id not in self.characters:
            return []

        target_char = self.characters[character_id]
        target_vector = PersonalityVector(**target_char["personality_vector"])

        similarities = []
        for other_id, other_char in self.characters.items():
            if other_id == character_id:
                continue

            other_vector = PersonalityVector(**other_char["personality_vector"])
            similarity = target_vector.similarity(other_vector)
            similarities.append((other_id, similarity))

        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:limit]

    def get_characters_by_archetype(self, archetype: str) -> List[Dict[str, Any]]:
        """获取指定原型的所有角色"""
        character_ids = self.archetype_index.get(archetype, [])
        return [self.characters[char_id] for char_id in character_ids if char_id in self.characters]

    def get_characters_by_role(self, role: str) -> List[Dict[str, Any]]:
        """获取指定叙事角色的所有角色"""
        character_ids = self.role_index.get(role, [])
        return [self.characters[char_id] for char_id in character_ids if char_id in self.characters]

    def get_characters_by_tag(self, tag: str) -> List[Dict[str, Any]]:
        """获取指定标签的所有角色"""
        character_ids = self.tag_index.get(tag, [])
        return [self.characters[char_id] for char_id in character_ids if char_id in self.characters]

    def get_all_archetypes(self) -> List[str]:
        """获取所有使用的原型"""
        return list(self.archetype_index.keys())

    def get_all_roles(self) -> List[str]:
        """获取所有使用的叙事角色"""
        return list(self.role_index.keys())

    def get_all_tags(self) -> List[str]:
        """获取所有使用的标签"""
        return list(self.tag_index.keys())

    def get_statistics(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        return {
            "total_characters": len(self.characters),
            "archetypes": {archetype: len(chars) for archetype, chars in self.archetype_index.items()},
            "roles": {role: len(chars) for role, chars in self.role_index.items()},
            "tags": {tag: len(chars) for tag, chars in self.tag_index.items()},
            "total_changes": sum(len(history) for history in self.development_history.values()),
            "average_changes_per_character": sum(len(history) for history in self.development_history.values()) / max(1, len(self.characters))
        }

    def export_character_data(self, character_id: str) -> Optional[Dict[str, Any]]:
        """导出单个角色的完整数据"""
        if character_id not in self.characters:
            return None

        return {
            "character": self.characters[character_id],
            "development_history": self.get_character_development_history(character_id),
            "growth_analysis": self.analyze_character_growth(character_id),
            "similar_characters": self.find_similar_characters(character_id)
        }

    def batch_update_characters(self, updates: Dict[str, Dict[str, Any]]) -> Dict[str, bool]:
        """批量更新角色"""
        results = {}
        for character_id, update_data in updates.items():
            results[character_id] = self.update_character(character_id, update_data)
        return results
