from __future__ import annotations
from typing import Dict, List, Optional, Tuple

try:
    import networkx as nx
except Exception:  # pragma: no cover
    nx = None  # type: ignore

from .storage import Storage


class GraphIndex:
    """Builds an in-memory multi-graph for relationship and event reasoning."""

    def __init__(self, storage: Storage):
        self.storage = storage
        self.G = None

    def build(self):
        if nx is None:
            self.G = None
            return
        G = nx.MultiDiGraph()
        # Characters
        rows = self.storage._conn.execute("SELECT id, name FROM characters").fetchall()
        for r in rows:
            G.add_node(("character", r["id"]), label=r["name"], kind="character")
        # Events
        rows = self.storage._conn.execute("SELECT id, title, sequence, start_date FROM events").fetchall()
        for r in rows:
            G.add_node(("event", r["id"]), label=r["title"], kind="event", sequence=r["sequence"], start=r["start_date"])
        # Relations
        rows = self.storage._conn.execute("SELECT source_id, target_id, type FROM relations").fetchall()
        for r in rows:
            G.add_edge(("character", r["source_id"]), ("character", r["target_id"]), kind="relation", type=r["type"])
        # Participation
        rows = self.storage._conn.execute("SELECT event_id, entity_type, entity_id, role FROM event_participants").fetchall()
        for r in rows:
            if r["entity_type"] == "character":
                G.add_edge(("character", r["entity_id"]), ("event", r["event_id"]), kind="participates", role=r["role"])
        # Event links
        rows = self.storage._conn.execute("SELECT src_event_id, dst_event_id, type, weight FROM event_links").fetchall()
        for r in rows:
            G.add_edge(("event", r["src_event_id"]), ("event", r["dst_event_id"]), kind="event_link", type=r["type"], weight=r["weight"])
        self.G = G

    # --- Queries ---
    def path_between_characters(self, a_id: str, b_id: str, max_len: int = 4) -> Optional[List[Tuple]]:
        if self.G is None or nx is None:
            return None
        src = ("character", a_id)
        dst = ("character", b_id)
        try:
            path = nx.shortest_path(self.G, src, dst)
        except Exception:
            return None
        if len(path) - 1 > max_len:
            return None
        return path

    def ego_network(self, char_id: str, radius: int = 1):
        if self.G is None or nx is None:
            return None
        node = ("character", char_id)
        if node not in self.G:
            return None
        return nx.ego_graph(self.G, node, radius=radius)

