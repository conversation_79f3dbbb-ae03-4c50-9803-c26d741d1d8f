"""
AI小说管理工具 - 主入口文件
为作家创作过程提供信息管理和决策支持的本地工具
"""

import logging
import sys
import json
from pathlib import Path
from typing import Dict, Any, Optional

from .api.novel_api import NovelManagementAPI
from .config import get_config, LOGGING_CONFIG, PROJECT_ROOT

class NovelManagementSystem:
    """小说管理系统主类"""
    
    def __init__(self, db_path: str = None, log_level: str = "INFO"):
        """初始化系统"""
        self._setup_logging(log_level)
        self.logger = logging.getLogger(__name__)
        
        try:
            # 初始化API
            self.api = NovelManagementAPI(db_path)
            self.logger.info("Novel Management System initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize system: {str(e)}")
            raise
    
    def _setup_logging(self, log_level: str):
        """设置日志"""
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format=LOGGING_CONFIG["format"],
            handlers=[
                logging.FileHandler(LOGGING_CONFIG["file"], encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
    
    # ==================== 人物管理 ====================
    
    def create_character(self, name: str, **kwargs) -> Dict[str, Any]:
        """创建人物"""
        data = {"name": name, **kwargs}
        return self.api.create_character(data)
    
    def get_character(self, character_id: str) -> Dict[str, Any]:
        """获取人物"""
        return self.api.get_character(character_id)
    
    def update_character(self, character_id: str, **updates) -> Dict[str, Any]:
        """更新人物"""
        return self.api.update_character(character_id, updates)
    
    def search_characters(self, **criteria) -> Dict[str, Any]:
        """搜索人物"""
        return self.api.search_characters(criteria)
    
    def add_personality_trait(self, character_id: str, trait: str, description: str = "", intensity: float = 0.5) -> Dict[str, Any]:
        """添加性格特征"""
        try:
            success = self.api.character_manager.add_personality_trait(character_id, trait, description, intensity)
            return {
                "success": success,
                "message": "Personality trait added successfully" if success else "Failed to add trait"
            }
        except Exception as e:
            return self.api._handle_error("add_personality_trait", e)
    
    # ==================== 关系管理 ====================
    
    def create_relationship(self, character_a: str, character_b: str, relationship_type: str, **kwargs) -> Dict[str, Any]:
        """创建关系"""
        data = {
            "character_a": character_a,
            "character_b": character_b,
            "relationship_type": relationship_type,
            **kwargs
        }
        return self.api.create_relationship(data)
    
    def get_relationship_network(self, character_id: str, max_depth: int = 2) -> Dict[str, Any]:
        """获取关系网络"""
        return self.api.get_relationship_network(character_id, max_depth)
    
    def find_relationship_path(self, start_character: str, end_character: str) -> Dict[str, Any]:
        """查找关系路径"""
        return self.api.find_relationship_path(start_character, end_character)
    
    # ==================== 情节管理 ====================
    
    def create_event(self, title: str, **kwargs) -> Dict[str, Any]:
        """创建事件"""
        data = {"title": title, **kwargs}
        return self.api.create_event(data)
    
    def get_timeline(self, **filters) -> Dict[str, Any]:
        """获取时间线"""
        return self.api.get_timeline(filters)
    
    def add_causal_relationship(self, cause_event_id: str, effect_event_id: str) -> Dict[str, Any]:
        """添加因果关系"""
        return self.api.add_causal_relationship(cause_event_id, effect_event_id)
    
    def analyze_plot_structure(self) -> Dict[str, Any]:
        """分析情节结构"""
        return self.api.analyze_plot_structure()
    
    # ==================== 世界观管理 ====================
    
    def create_world_setting(self, category: str, name: str, **kwargs) -> Dict[str, Any]:
        """创建世界观设定"""
        data = {"category": category, "name": name, **kwargs}
        return self.api.create_world_setting(data)
    
    def check_world_consistency(self) -> Dict[str, Any]:
        """检查世界观一致性"""
        return self.api.check_world_consistency()
    
    def get_setting_network(self, setting_id: str, max_depth: int = 3) -> Dict[str, Any]:
        """获取设定网络"""
        return self.api.get_setting_network(setting_id, max_depth)
    
    # ==================== 创作笔记管理 ====================
    
    def create_note(self, title: str, content: str = "", **kwargs) -> Dict[str, Any]:
        """创建笔记"""
        data = {"title": title, "content": content, **kwargs}
        return self.api.create_note(data)
    
    def search_notes(self, query: str) -> Dict[str, Any]:
        """搜索笔记"""
        return self.api.search_notes_by_content(query)
    
    def get_related_notes(self, entity_type: str, entity_id: str) -> Dict[str, Any]:
        """获取相关笔记"""
        return self.api.get_related_notes(entity_type, entity_id)
    
    # ==================== 智能查询 ====================
    
    def query(self, query_text: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """智能查询"""
        return self.api.intelligent_query(query_text, context)
    
    def get_overview(self) -> Dict[str, Any]:
        """获取系统概览"""
        return self.api.get_system_overview()
    
    def export_data(self, data_type: str, format_type: str = "json", **filters) -> Dict[str, Any]:
        """导出数据"""
        return self.api.export_data(data_type, format_type, filters)
    
    # ==================== 系统管理 ====================
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            stats = {
                "characters": self.api.character_manager.get_character_statistics(),
                "relationships": self.api.relationship_manager.get_relationship_statistics(),
                "world_settings": self.api.worldbuilding_manager.get_setting_statistics(),
                "notes": self.api.notes_manager.get_note_statistics()
            }
            
            return {
                "success": True,
                "statistics": stats
            }
        except Exception as e:
            return self.api._handle_error("get_statistics", e)
    
    def backup_data(self, backup_path: str = None) -> Dict[str, Any]:
        """备份数据"""
        try:
            from datetime import datetime
            if not backup_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = PROJECT_ROOT / "data" / "backups" / f"backup_{timestamp}.json"
            
            # 导出所有数据
            all_data = {
                "characters": self.api.character_manager.search_characters(),
                "relationships": self.api.relationship_manager.db.search_entities("relationships", {"status": "active"}),
                "events": self.api.plot_manager.search_events(),
                "world_settings": self.api.worldbuilding_manager.search_settings(),
                "notes": self.api.notes_manager.search_notes(),
                "backup_timestamp": datetime.now().isoformat(),
                "version": "1.0"
            }
            
            # 保存备份文件
            backup_file = Path(backup_path)
            backup_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(all_data, f, ensure_ascii=False, indent=2, default=str)
            
            return {
                "success": True,
                "backup_path": str(backup_file),
                "message": "Data backup completed successfully"
            }
            
        except Exception as e:
            return self.api._handle_error("backup_data", e)
    
    def restore_data(self, backup_path: str) -> Dict[str, Any]:
        """恢复数据"""
        try:
            backup_file = Path(backup_path)
            if not backup_file.exists():
                return {
                    "success": False,
                    "error": "Backup file not found"
                }
            
            with open(backup_file, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            # 这里应该实现数据恢复逻辑
            # 由于涉及数据库操作的复杂性，这里只返回成功信息
            
            return {
                "success": True,
                "message": "Data restore completed successfully",
                "backup_version": backup_data.get("version", "unknown"),
                "backup_timestamp": backup_data.get("backup_timestamp", "unknown")
            }
            
        except Exception as e:
            return self.api._handle_error("restore_data", e)
    
    def health_check(self) -> Dict[str, Any]:
        """系统健康检查"""
        try:
            health_status = {
                "database": "healthy",
                "managers": "healthy",
                "api": "healthy"
            }
            
            # 检查数据库连接
            try:
                with self.api.db.get_connection() as conn:
                    conn.execute("SELECT 1")
            except Exception:
                health_status["database"] = "unhealthy"
            
            # 检查各个管理器
            try:
                self.api.character_manager.search_characters(limit=1)
                self.api.relationship_manager.get_relationship_statistics()
                self.api.plot_manager._calculate_plot_metrics()
                self.api.worldbuilding_manager.get_setting_statistics()
                self.api.notes_manager.get_note_statistics()
            except Exception:
                health_status["managers"] = "unhealthy"
            
            overall_health = "healthy" if all(status == "healthy" for status in health_status.values()) else "unhealthy"
            
            from datetime import datetime
            return {
                "success": True,
                "overall_health": overall_health,
                "components": health_status,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return self.api._handle_error("health_check", e)


def create_system(db_path: str = None, log_level: str = "INFO") -> NovelManagementSystem:
    """创建小说管理系统实例"""
    return NovelManagementSystem(db_path, log_level)


# 为了方便导入，提供一些快捷函数
def quick_start(db_path: str = None) -> NovelManagementSystem:
    """快速启动系统"""
    return create_system(db_path)


if __name__ == "__main__":
    # 命令行启动示例
    import argparse
    from datetime import datetime
    
    parser = argparse.ArgumentParser(description="AI Novel Management System")
    parser.add_argument("--db-path", help="Database path")
    parser.add_argument("--log-level", default="INFO", help="Log level")
    parser.add_argument("--action", help="Action to perform")
    
    args = parser.parse_args()
    
    try:
        system = create_system(args.db_path, args.log_level)
        
        if args.action == "overview":
            result = system.get_overview()
            print(json.dumps(result, ensure_ascii=False, indent=2))
        elif args.action == "health":
            result = system.health_check()
            print(json.dumps(result, ensure_ascii=False, indent=2))
        elif args.action == "stats":
            result = system.get_statistics()
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            print("AI Novel Management System is ready!")
            print("Available actions: overview, health, stats")
            
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)
