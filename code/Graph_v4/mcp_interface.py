"""
AI小说管理工具 - MCP兼容接口
为AI Agent提供标准化的MCP协议接口
"""

import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from .main import NovelManagementSystem

class MCPNovelInterface:
    """MCP兼容的小说管理接口"""
    
    def __init__(self, db_path: str = None):
        """初始化MCP接口"""
        self.system = NovelManagementSystem(db_path)
        self.logger = logging.getLogger(__name__)
        
        # MCP工具定义
        self.tools = {
            "create_character": {
                "description": "创建新的小说人物",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "name": {"type": "string", "description": "人物姓名"},
                        "age": {"type": "integer", "description": "年龄"},
                        "gender": {"type": "string", "description": "性别"},
                        "background": {"type": "string", "description": "背景故事"},
                        "personality_traits": {"type": "array", "description": "性格特征列表"},
                        "appearance": {"type": "string", "description": "外貌描述"},
                        "role": {"type": "string", "description": "角色定位"}
                    },
                    "required": ["name"]
                }
            },
            
            "search_characters": {
                "description": "搜索小说人物",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "name": {"type": "string", "description": "人物姓名"},
                        "role": {"type": "string", "description": "角色定位"},
                        "gender": {"type": "string", "description": "性别"},
                        "limit": {"type": "integer", "description": "返回结果数量限制", "default": 10}
                    }
                }
            },
            
            "create_relationship": {
                "description": "创建人物关系",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "character_a": {"type": "string", "description": "人物A的ID"},
                        "character_b": {"type": "string", "description": "人物B的ID"},
                        "relationship_type": {"type": "string", "description": "关系类型"},
                        "description": {"type": "string", "description": "关系描述"},
                        "strength": {"type": "number", "description": "关系强度(0-1)", "default": 0.5}
                    },
                    "required": ["character_a", "character_b", "relationship_type"]
                }
            },
            
            "get_relationship_network": {
                "description": "获取人物关系网络",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "character_id": {"type": "string", "description": "中心人物ID"},
                        "max_depth": {"type": "integer", "description": "网络深度", "default": 2}
                    },
                    "required": ["character_id"]
                }
            },
            
            "create_event": {
                "description": "创建情节事件",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "title": {"type": "string", "description": "事件标题"},
                        "description": {"type": "string", "description": "事件描述"},
                        "event_type": {"type": "string", "description": "事件类型"},
                        "timeline_position": {"type": "string", "description": "时间线位置"},
                        "involved_characters": {"type": "array", "description": "参与人物ID列表"},
                        "location": {"type": "string", "description": "发生地点"},
                        "importance": {"type": "integer", "description": "重要性(1-5)", "default": 3}
                    },
                    "required": ["title"]
                }
            },
            
            "get_timeline": {
                "description": "获取故事时间线",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "start_date": {"type": "string", "description": "开始日期"},
                        "end_date": {"type": "string", "description": "结束日期"},
                        "event_type": {"type": "string", "description": "事件类型过滤"},
                        "character_id": {"type": "string", "description": "相关人物ID"}
                    }
                }
            },
            
            "create_world_setting": {
                "description": "创建世界观设定",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "category": {"type": "string", "description": "设定分类"},
                        "name": {"type": "string", "description": "设定名称"},
                        "description": {"type": "string", "description": "设定描述"},
                        "rules": {"type": "array", "description": "相关规则列表"},
                        "related_settings": {"type": "array", "description": "相关设定ID列表"}
                    },
                    "required": ["category", "name"]
                }
            },
            
            "check_world_consistency": {
                "description": "检查世界观一致性",
                "parameters": {
                    "type": "object",
                    "properties": {}
                }
            },
            
            "create_note": {
                "description": "创建创作笔记",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "title": {"type": "string", "description": "笔记标题"},
                        "content": {"type": "string", "description": "笔记内容"},
                        "note_type": {"type": "string", "description": "笔记类型", "default": "idea"},
                        "priority": {"type": "integer", "description": "优先级(1-5)", "default": 3},
                        "related_characters": {"type": "array", "description": "相关人物ID列表"},
                        "related_events": {"type": "array", "description": "相关事件ID列表"},
                        "related_settings": {"type": "array", "description": "相关设定ID列表"}
                    },
                    "required": ["title"]
                }
            },
            
            "search_notes": {
                "description": "搜索创作笔记",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "搜索关键词"},
                        "note_type": {"type": "string", "description": "笔记类型"},
                        "priority": {"type": "integer", "description": "优先级"},
                        "limit": {"type": "integer", "description": "返回结果数量限制", "default": 20}
                    }
                }
            },
            
            "intelligent_query": {
                "description": "智能查询系统",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "自然语言查询"},
                        "context": {"type": "object", "description": "查询上下文"}
                    },
                    "required": ["query"]
                }
            },
            
            "get_system_overview": {
                "description": "获取系统概览",
                "parameters": {
                    "type": "object",
                    "properties": {}
                }
            },
            
            "export_data": {
                "description": "导出数据",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "data_type": {"type": "string", "description": "数据类型(characters/relationships/events/world_settings/notes)"},
                        "format": {"type": "string", "description": "导出格式(json/csv)", "default": "json"},
                        "filters": {"type": "object", "description": "过滤条件"}
                    },
                    "required": ["data_type"]
                }
            },
            
            "analyze_plot_structure": {
                "description": "分析情节结构",
                "parameters": {
                    "type": "object",
                    "properties": {}
                }
            },
            
            "get_statistics": {
                "description": "获取系统统计信息",
                "parameters": {
                    "type": "object",
                    "properties": {}
                }
            }
        }
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取可用工具列表"""
        return [
            {
                "name": name,
                "description": tool["description"],
                "inputSchema": tool["parameters"]
            }
            for name, tool in self.tools.items()
        ]
    
    def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用工具"""
        try:
            if tool_name not in self.tools:
                return {
                    "success": False,
                    "error": f"Unknown tool: {tool_name}",
                    "available_tools": list(self.tools.keys())
                }
            
            # 验证参数
            validation_result = self._validate_arguments(tool_name, arguments)
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "error": f"Invalid arguments: {validation_result['error']}",
                    "expected_schema": self.tools[tool_name]["parameters"]
                }
            
            # 调用相应的方法
            result = self._execute_tool(tool_name, arguments)
            
            # 添加元数据
            result["tool_name"] = tool_name
            result["timestamp"] = datetime.now().isoformat()
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error calling tool {tool_name}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "tool_name": tool_name,
                "timestamp": datetime.now().isoformat()
            }
    
    def _validate_arguments(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """验证工具参数"""
        schema = self.tools[tool_name]["parameters"]
        required_fields = schema.get("required", [])
        properties = schema.get("properties", {})
        
        # 检查必需字段
        for field in required_fields:
            if field not in arguments:
                return {
                    "valid": False,
                    "error": f"Missing required field: {field}"
                }
        
        # 检查字段类型（简单验证）
        for field, value in arguments.items():
            if field in properties:
                expected_type = properties[field].get("type")
                if expected_type == "string" and not isinstance(value, str):
                    return {
                        "valid": False,
                        "error": f"Field '{field}' must be a string"
                    }
                elif expected_type == "integer" and not isinstance(value, int):
                    return {
                        "valid": False,
                        "error": f"Field '{field}' must be an integer"
                    }
                elif expected_type == "number" and not isinstance(value, (int, float)):
                    return {
                        "valid": False,
                        "error": f"Field '{field}' must be a number"
                    }
                elif expected_type == "array" and not isinstance(value, list):
                    return {
                        "valid": False,
                        "error": f"Field '{field}' must be an array"
                    }
                elif expected_type == "object" and not isinstance(value, dict):
                    return {
                        "valid": False,
                        "error": f"Field '{field}' must be an object"
                    }
        
        return {"valid": True}
    
    def _execute_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """执行工具"""
        # 人物管理
        if tool_name == "create_character":
            return self.system.create_character(**arguments)
        elif tool_name == "search_characters":
            return self.system.search_characters(**arguments)
        
        # 关系管理
        elif tool_name == "create_relationship":
            return self.system.create_relationship(**arguments)
        elif tool_name == "get_relationship_network":
            return self.system.get_relationship_network(**arguments)
        
        # 情节管理
        elif tool_name == "create_event":
            return self.system.create_event(**arguments)
        elif tool_name == "get_timeline":
            return self.system.get_timeline(**arguments)
        elif tool_name == "analyze_plot_structure":
            return self.system.analyze_plot_structure()
        
        # 世界观管理
        elif tool_name == "create_world_setting":
            return self.system.create_world_setting(**arguments)
        elif tool_name == "check_world_consistency":
            return self.system.check_world_consistency()
        
        # 创作笔记
        elif tool_name == "create_note":
            return self.system.create_note(**arguments)
        elif tool_name == "search_notes":
            query = arguments.get("query", "")
            if query:
                return self.system.search_notes(query)
            else:
                # 如果没有查询词，使用其他条件搜索
                criteria = {k: v for k, v in arguments.items() if k != "limit"}
                return self.system.api.notes_manager.search_notes(criteria, arguments.get("limit", 20))
        
        # 智能查询
        elif tool_name == "intelligent_query":
            return self.system.query(arguments["query"], arguments.get("context"))
        
        # 系统功能
        elif tool_name == "get_system_overview":
            return self.system.get_overview()
        elif tool_name == "export_data":
            return self.system.export_data(
                arguments["data_type"],
                arguments.get("format", "json"),
                **arguments.get("filters", {})
            )
        elif tool_name == "get_statistics":
            return self.system.get_statistics()
        
        else:
            return {
                "success": False,
                "error": f"Tool implementation not found: {tool_name}"
            }
    
    def process_mcp_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理MCP请求"""
        try:
            method = request.get("method")
            params = request.get("params", {})
            
            if method == "tools/list":
                return {
                    "tools": self.get_available_tools()
                }
            elif method == "tools/call":
                tool_name = params.get("name")
                arguments = params.get("arguments", {})
                return self.call_tool(tool_name, arguments)
            else:
                return {
                    "error": f"Unknown method: {method}",
                    "supported_methods": ["tools/list", "tools/call"]
                }
                
        except Exception as e:
            self.logger.error(f"Error processing MCP request: {str(e)}")
            return {
                "error": str(e),
                "request": request
            }


def create_mcp_interface(db_path: str = None) -> MCPNovelInterface:
    """创建MCP接口实例"""
    return MCPNovelInterface(db_path)


# 示例用法
if __name__ == "__main__":
    # 创建MCP接口
    mcp = create_mcp_interface()
    
    # 获取可用工具
    tools = mcp.get_available_tools()
    print("Available tools:")
    for tool in tools:
        print(f"- {tool['name']}: {tool['description']}")
    
    # 示例调用
    result = mcp.call_tool("get_system_overview", {})
    print("\nSystem Overview:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
