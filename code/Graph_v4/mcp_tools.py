from __future__ import annotations
from typing import Any, Dict, List, Optional
from .storage import Storage
from .query import QueryManager
from .models import Character, Relation, Event, EventParticipant, EventLink, WorldEntry, Note


class MCPToolkit:
    """Lightweight MCP-style tool registry for local calls.
    Each tool is a method taking a dict payload and returning a dict result.
    """

    def __init__(self, storage: Optional[Storage] = None):
        self.storage = storage or Storage()
        self.q = QueryManager(self.storage)

    # ---- Tool dispatch ----
    def list_tools(self) -> List[str]:
        return [
            "search",
            "get_character",
            "upsert_character",
            "link_relation",
            "add_event",
            "get_timeline",
            "add_note",
            "check_consistency",
        ]

    def call(self, tool: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        fn = getattr(self, f"tool_{tool}", None)
        if not fn:
            return {"error": f"unknown tool: {tool}"}
        return fn(payload)

    # ---- Tools ----
    def tool_search(self, p: Dict[str, Any]) -> Dict[str, Any]:
        q = p.get("q", "")
        limit = int(p.get("limit", 20))
        return {"ok": True, "results": self.q.search(q, limit)}

    def tool_get_character(self, p: Dict[str, Any]) -> Dict[str, Any]:
        prof = self.q.get_character_profile(id=p.get("id"), name=p.get("name"))
        if not prof:
            return {"ok": False, "error": "character not found"}
        return {"ok": True, "profile": prof}

    def tool_upsert_character(self, p: Dict[str, Any]) -> Dict[str, Any]:
        ch = Character(
            id=p.get("id") or Character().id,
            name=p.get("name", ""),
            aliases=p.get("aliases", []) or [],
            summary=p.get("summary", ""),
            tags=p.get("tags", []) or [],
            meta=p.get("meta", {}) or {},
        )
        cid = self.storage.upsert_character(ch)
        return {"ok": True, "id": cid}

    def tool_link_relation(self, p: Dict[str, Any]) -> Dict[str, Any]:
        rel = Relation(
            id=p.get("id") or Relation().id,
            source_id=p.get("source_id"),
            target_id=p.get("target_id"),
            type=p.get("type", "related"),
            since=p.get("since"),
            until=p.get("until"),
            description=p.get("description", ""),
            strength=p.get("strength"),
            meta=p.get("meta", {}),
        )
        rid = self.storage.upsert_relation(rel)
        return {"ok": True, "id": rid}

    def tool_add_event(self, p: Dict[str, Any]) -> Dict[str, Any]:
        ev = Event(
            id=p.get("id") or Event().id,
            title=p.get("title", ""),
            summary=p.get("summary", ""),
            arc=p.get("arc"),
            location=p.get("location"),
            start_date=p.get("start_date"),
            end_date=p.get("end_date"),
            sequence=p.get("sequence"),
            meta=p.get("meta", {}),
        )
        eid = self.storage.upsert_event(ev)
        # participants
        for part in p.get("participants", []) or []:
            ep = EventParticipant(
                event_id=eid,
                entity_type=part.get("entity_type", "character"),
                entity_id=part.get("entity_id"),
                role=part.get("role"),
                importance=part.get("importance"),
                meta=part.get("meta", {}),
            )
            self.storage.add_event_participant(ep)
        # links
        for link in p.get("links", []) or []:
            el = EventLink(
                src_event_id=eid if link.get("direction", "out") == "out" else link.get("src_event_id"),
                dst_event_id=link.get("dst_event_id") if link.get("direction", "out") == "out" else eid,
                type=link.get("type", "FOLLOWS"),
                weight=link.get("weight"),
                description=link.get("description", ""),
            )
            self.storage.link_events(el)
        return {"ok": True, "id": eid}

    def tool_get_timeline(self, p: Dict[str, Any]) -> Dict[str, Any]:
        res = self.q.get_timeline(character_id=p.get("character_id"), arc=p.get("arc"), limit=int(p.get("limit", 100)))
        return {"ok": True, "events": res}

    def tool_add_note(self, p: Dict[str, Any]) -> Dict[str, Any]:
        note = Note(
            content=p.get("content", ""),
            linked_entity_type=p.get("linked_entity_type"),
            linked_entity_id=p.get("linked_entity_id"),
            tags=p.get("tags", []) or [],
        )
        nid = self.storage.add_note(note)
        return {"ok": True, "id": nid}

    def tool_check_consistency(self, p: Dict[str, Any]) -> Dict[str, Any]:
        return {"ok": True, "issues": self.q.check_consistency()}

