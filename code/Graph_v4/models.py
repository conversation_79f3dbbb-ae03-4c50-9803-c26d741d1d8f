from __future__ import annotations
from dataclasses import dataclass, field, asdict
from typing import List, Optional, Dict, Any
from uuid import uuid4
from datetime import datetime


def now_iso() -> str:
    return datetime.utcnow().isoformat(timespec="seconds") + "Z"


def gen_id() -> str:
    return uuid4().hex


@dataclass
class Character:
    id: str = field(default_factory=gen_id)
    name: str = ""
    aliases: List[str] = field(default_factory=list)
    summary: str = ""
    tags: List[str] = field(default_factory=list)
    meta: Dict[str, Any] = field(default_factory=dict)
    created_at: str = field(default_factory=now_iso)
    updated_at: str = field(default_factory=now_iso)

    def to_record(self) -> Dict[str, Any]:
        d = asdict(self)
        return d


@dataclass
class Relation:
    id: str = field(default_factory=gen_id)
    source_id: str = ""
    target_id: str = ""
    type: str = "related"
    since: Optional[str] = None
    until: Optional[str] = None
    description: str = ""
    strength: Optional[int] = None
    meta: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Event:
    id: str = field(default_factory=gen_id)
    title: str = ""
    summary: str = ""
    arc: Optional[str] = None
    location: Optional[str] = None
    start_date: Optional[str] = None  # ISO date or datetime
    end_date: Optional[str] = None
    sequence: Optional[int] = None  # fallback ordering within arc or global
    meta: Dict[str, Any] = field(default_factory=dict)
    created_at: str = field(default_factory=now_iso)
    updated_at: str = field(default_factory=now_iso)


@dataclass
class EventParticipant:
    id: str = field(default_factory=gen_id)
    event_id: str = ""
    entity_type: str = "character"  # character/world
    entity_id: str = ""
    role: Optional[str] = None
    importance: Optional[int] = None
    meta: Dict[str, Any] = field(default_factory=dict)


@dataclass
class EventLink:
    id: str = field(default_factory=gen_id)
    src_event_id: str = ""
    dst_event_id: str = ""
    type: str = "FOLLOWS"  # CAUSES/FORESHADOWS/FOLLOWS
    weight: Optional[float] = None
    description: str = ""


@dataclass
class WorldEntry:
    id: str = field(default_factory=gen_id)
    title: str = ""
    category: Optional[str] = None
    content: str = ""
    parent_id: Optional[str] = None
    tags: List[str] = field(default_factory=list)
    meta: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Note:
    id: str = field(default_factory=gen_id)
    content: str = ""
    linked_entity_type: Optional[str] = None
    linked_entity_id: Optional[str] = None
    tags: List[str] = field(default_factory=list)
    created_at: str = field(default_factory=now_iso)


Entity = Dict[str, Any]

