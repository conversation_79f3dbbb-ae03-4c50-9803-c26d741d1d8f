from __future__ import annotations
from typing import Any, Dict, List, Optional
from .storage import Storage
from .graph_index import GraphIndex


class QueryManager:
    def __init__(self, storage: Storage):
        self.storage = storage
        self.graph = GraphIndex(storage)

    def refresh_indexes(self):
        self.graph.build()

    # --- High-level queries ---
    def get_character_profile(self, *, id: Optional[str] = None, name: Optional[str] = None) -> Optional[Dict[str, Any]]:
        ch = None
        if id:
            ch = self.storage.get_character(id)
        elif name:
            ch = self.storage.get_character_by_name(name)
        if not ch:
            return None
        rels = self.storage.get_relations_for_character(ch["id"])
        events = self.storage.get_events_for_character(ch["id"])  # ordered
        notes = self.storage.get_notes_for_entity("character", ch["id"])
        return {"character": ch, "relations": rels, "timeline": events, "notes": notes}

    def search(self, q: str, limit: int = 20) -> Dict[str, List[Dict[str, Any]]]:
        return self.storage.search_all(q, limit)

    def get_timeline(self, *, character_id: Optional[str] = None, arc: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        if character_id:
            res = self.storage.get_events_for_character(character_id, arc=arc)
        else:
            sql = "SELECT * FROM events ORDER BY COALESCE(sequence, 1e15), COALESCE(start_date, '9999-12-31') LIMIT ?"
            rows = self.storage._conn.execute(sql, (limit,)).fetchall()
            res = [dict(r) for r in rows]
        return res[:limit]

    def check_consistency(self) -> List[Dict[str, Any]]:
        return self.storage.consistency_checks()

