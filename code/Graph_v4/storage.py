from __future__ import annotations
import sqlite3, json
from pathlib import Path
from typing import Any, Dict, Iterable, List, Optional, Tuple
from .models import Character, Relation, Event, EventParticipant, EventLink, WorldEntry, Note


class Storage:
    def __init__(self, db_path: str | Path = None):
        base = Path(__file__).resolve().parent
        self.db_path = Path(db_path) if db_path else (base / "novel.db")
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._conn = sqlite3.connect(self.db_path)
        self._conn.row_factory = sqlite3.Row
        self._ensure_schema()

    def close(self):
        try:
            self._conn.close()
        except Exception:
            pass

    # --- Schema ---
    def _ensure_schema(self):
        c = self._conn.cursor()
        c.executescript(
            """
            PRAGMA journal_mode=WAL;
            CREATE TABLE IF NOT EXISTS characters (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                aliases TEXT,
                summary TEXT,
                tags TEXT,
                meta TEXT,
                created_at TEXT,
                updated_at TEXT
            );
            CREATE VIRTUAL TABLE IF NOT EXISTS characters_fts USING fts5(id, name, aliases, summary);

            CREATE TABLE IF NOT EXISTS relations (
                id TEXT PRIMARY KEY,
                source_id TEXT,
                target_id TEXT,
                type TEXT,
                since TEXT,
                until TEXT,
                description TEXT,
                strength INTEGER,
                meta TEXT
            );

            CREATE TABLE IF NOT EXISTS events (
                id TEXT PRIMARY KEY,
                title TEXT,
                summary TEXT,
                arc TEXT,
                location TEXT,
                start_date TEXT,
                end_date TEXT,
                sequence INTEGER,
                meta TEXT,
                created_at TEXT,
                updated_at TEXT
            );
            CREATE VIRTUAL TABLE IF NOT EXISTS events_fts USING fts5(id, title, summary, arc, location);

            CREATE TABLE IF NOT EXISTS event_participants (
                id TEXT PRIMARY KEY,
                event_id TEXT,
                entity_type TEXT,
                entity_id TEXT,
                role TEXT,
                importance INTEGER,
                meta TEXT
            );

            CREATE TABLE IF NOT EXISTS event_links (
                id TEXT PRIMARY KEY,
                src_event_id TEXT,
                dst_event_id TEXT,
                type TEXT,
                weight REAL,
                description TEXT
            );

            CREATE TABLE IF NOT EXISTS world_entries (
                id TEXT PRIMARY KEY,
                title TEXT,
                category TEXT,
                content TEXT,
                parent_id TEXT,
                tags TEXT,
                meta TEXT
            );
            CREATE VIRTUAL TABLE IF NOT EXISTS world_fts USING fts5(id, title, category, content, tags);

            CREATE TABLE IF NOT EXISTS notes (
                id TEXT PRIMARY KEY,
                content TEXT,
                linked_entity_type TEXT,
                linked_entity_id TEXT,
                tags TEXT,
                created_at TEXT
            );
            CREATE VIRTUAL TABLE IF NOT EXISTS notes_fts USING fts5(id, content, tags);
            """
        )
        self._conn.commit()

    # --- Helpers ---
    @staticmethod
    def _dumps(v: Any) -> Optional[str]:
        if v is None:
            return None
        return json.dumps(v, ensure_ascii=False)

    @staticmethod
    def _loads(s: Optional[str]) -> Any:
        if s is None:
            return None
        return json.loads(s)

    # --- Characters ---
    def upsert_character(self, ch: Character) -> str:
        c = self._conn.cursor()
        c.execute(
            """
            INSERT INTO characters(id,name,aliases,summary,tags,meta,created_at,updated_at)
            VALUES(?,?,?,?,?,?,?,?)
            ON CONFLICT(id) DO UPDATE SET
              name=excluded.name,
              aliases=excluded.aliases,
              summary=excluded.summary,
              tags=excluded.tags,
              meta=excluded.meta,
              updated_at=excluded.updated_at
            """,
            (
                ch.id,
                ch.name,
                self._dumps(ch.aliases),
                ch.summary,
                self._dumps(ch.tags),
                self._dumps(ch.meta),
                ch.created_at,
                ch.updated_at,
            ),
        )
        c.execute(
            "REPLACE INTO characters_fts(id,name,aliases,summary) VALUES(?,?,?,?)",
            (ch.id, ch.name, ",".join(ch.aliases), ch.summary),
        )
        self._conn.commit()
        return ch.id

    def get_character(self, id: str) -> Optional[Dict[str, Any]]:
        r = self._conn.execute("SELECT * FROM characters WHERE id=?", (id,)).fetchone()
        if not r:
            return None
        return self._row_to_character(r)

    def get_character_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        r = self._conn.execute("SELECT * FROM characters WHERE name=?", (name,)).fetchone()
        return self._row_to_character(r) if r else None

    def search_characters(self, q: str, limit: int = 20) -> List[Dict[str, Any]]:
        sql = (
            "SELECT c.*, bm25(cf) as rank FROM characters_fts cf "
            "JOIN characters c ON c.id = cf.id "
            "WHERE cf MATCH ? ORDER BY rank LIMIT ?"
        )
        rows = self._conn.execute(sql, (q, limit)).fetchall()
        return [self._row_to_character(r) for r in rows]

    def _row_to_character(self, r: sqlite3.Row) -> Dict[str, Any]:
        return {
            "id": r["id"],
            "name": r["name"],
            "aliases": self._loads(r["aliases"]) or [],
            "summary": r["summary"] or "",
            "tags": self._loads(r["tags"]) or [],
            "meta": self._loads(r["meta"]) or {},
            "created_at": r["created_at"],
            "updated_at": r["updated_at"],
        }

    # --- Relations ---
    def upsert_relation(self, rel: Relation) -> str:
        self._conn.execute(
            """
            INSERT INTO relations(id,source_id,target_id,type,since,until,description,strength,meta)
            VALUES(?,?,?,?,?,?,?,?,?)
            ON CONFLICT(id) DO UPDATE SET
              source_id=excluded.source_id,
              target_id=excluded.target_id,
              type=excluded.type,
              since=excluded.since,
              until=excluded.until,
              description=excluded.description,
              strength=excluded.strength,
              meta=excluded.meta
            """,
            (
                rel.id,
                rel.source_id,
                rel.target_id,
                rel.type,
                rel.since,
                rel.until,
                rel.description,
                rel.strength,
                self._dumps(rel.meta),
            ),
        )
        self._conn.commit()
        return rel.id

    def get_relations_for_character(self, char_id: str) -> List[Dict[str, Any]]:
        rows = self._conn.execute(
            "SELECT * FROM relations WHERE source_id=? OR target_id=?",
            (char_id, char_id),
        ).fetchall()
        return [dict(r) for r in rows]

    # --- Events ---
    def upsert_event(self, ev: Event) -> str:
        self._conn.execute(
            """
            INSERT INTO events(id,title,summary,arc,location,start_date,end_date,sequence,meta,created_at,updated_at)
            VALUES(?,?,?,?,?,?,?,?,?,?,?)
            ON CONFLICT(id) DO UPDATE SET
              title=excluded.title,
              summary=excluded.summary,
              arc=excluded.arc,
              location=excluded.location,
              start_date=excluded.start_date,
              end_date=excluded.end_date,
              sequence=excluded.sequence,
              meta=excluded.meta,
              updated_at=excluded.updated_at
            """,
            (
                ev.id,
                ev.title,
                ev.summary,
                ev.arc,
                ev.location,
                ev.start_date,
                ev.end_date,
                ev.sequence,
                json.dumps(ev.meta, ensure_ascii=False),
                ev.created_at,
                ev.updated_at,
            ),
        )
        self._conn.execute(
            "REPLACE INTO events_fts(id,title,summary,arc,location) VALUES(?,?,?,?,?)",
            (ev.id, ev.title, ev.summary, ev.arc or "", ev.location or ""),
        )
        self._conn.commit()
        return ev.id

    def add_event_participant(self, p: EventParticipant) -> str:
        self._conn.execute(
            """
            INSERT INTO event_participants(id,event_id,entity_type,entity_id,role,importance,meta)
            VALUES(?,?,?,?,?,?,?)
            ON CONFLICT(id) DO UPDATE SET
              event_id=excluded.event_id,
              entity_type=excluded.entity_type,
              entity_id=excluded.entity_id,
              role=excluded.role,
              importance=excluded.importance,
              meta=excluded.meta
            """,
            (
                p.id,
                p.event_id,
                p.entity_type,
                p.entity_id,
                p.role,
                p.importance,
                json.dumps(p.meta, ensure_ascii=False),
            ),
        )
        self._conn.commit()
        return p.id

    def link_events(self, l: EventLink) -> str:
        self._conn.execute(
            """
            INSERT INTO event_links(id,src_event_id,dst_event_id,type,weight,description)
            VALUES(?,?,?,?,?,?)
            ON CONFLICT(id) DO UPDATE SET
              src_event_id=excluded.src_event_id,
              dst_event_id=excluded.dst_event_id,
              type=excluded.type,
              weight=excluded.weight,
              description=excluded.description
            """,
            (l.id, l.src_event_id, l.dst_event_id, l.type, l.weight, l.description),
        )
        self._conn.commit()
        return l.id

    def get_events_for_character(self, char_id: str, arc: Optional[str] = None) -> List[Dict[str, Any]]:
        sql = (
            "SELECT e.* FROM events e JOIN event_participants p ON e.id = p.event_id "
            "WHERE p.entity_type='character' AND p.entity_id=?"
        )
        params: List[Any] = [char_id]
        if arc:
            sql += " AND e.arc=?"
            params.append(arc)
        sql += " ORDER BY COALESCE(e.sequence, 1e15), COALESCE(e.start_date, '9999-12-31')"
        rows = self._conn.execute(sql, tuple(params)).fetchall()
        return [dict(r) for r in rows]

    # --- World ---
    def upsert_world_entry(self, we: WorldEntry) -> str:
        self._conn.execute(
            """
            INSERT INTO world_entries(id,title,category,content,parent_id,tags,meta)
            VALUES(?,?,?,?,?,?,?)
            ON CONFLICT(id) DO UPDATE SET
              title=excluded.title,
              category=excluded.category,
              content=excluded.content,
              parent_id=excluded.parent_id,
              tags=excluded.tags,
              meta=excluded.meta
            """,
            (
                we.id,
                we.title,
                we.category,
                we.content,
                we.parent_id,
                json.dumps(we.tags, ensure_ascii=False),
                json.dumps(we.meta, ensure_ascii=False),
            ),
        )
        self._conn.execute(
            "REPLACE INTO world_fts(id,title,category,content,tags) VALUES(?,?,?,?,?)",
            (we.id, we.title, we.category or "", we.content, ",".join(we.tags)),
        )
        self._conn.commit()
        return we.id

    def get_world_entry(self, id: str) -> Optional[Dict[str, Any]]:
        r = self._conn.execute("SELECT * FROM world_entries WHERE id=?", (id,)).fetchone()
        return dict(r) if r else None

    # --- Notes ---
    def add_note(self, note: Note) -> str:
        self._conn.execute(
            """
            INSERT INTO notes(id,content,linked_entity_type,linked_entity_id,tags,created_at)
            VALUES(?,?,?,?,?,?)
            ON CONFLICT(id) DO UPDATE SET
              content=excluded.content,
              linked_entity_type=excluded.linked_entity_type,
              linked_entity_id=excluded.linked_entity_id,
              tags=excluded.tags
            """,
            (
                note.id,
                note.content,
                note.linked_entity_type,
                note.linked_entity_id,
                json.dumps(note.tags, ensure_ascii=False),
                note.created_at,
            ),
        )
        self._conn.execute(
            "REPLACE INTO notes_fts(id,content,tags) VALUES(?,?,?)",
            (note.id, note.content, ",".join(note.tags)),
        )
        self._conn.commit()
        return note.id

    def get_notes_for_entity(self, entity_type: str, entity_id: str) -> List[Dict[str, Any]]:
        rows = self._conn.execute(
            "SELECT * FROM notes WHERE linked_entity_type=? AND linked_entity_id=? ORDER BY created_at DESC",
            (entity_type, entity_id),
        ).fetchall()
        return [dict(r) for r in rows]

    # --- Search ---
    def search_all(self, q: str, limit: int = 20) -> Dict[str, List[Dict[str, Any]]]:
        res: Dict[str, List[Dict[str, Any]]] = {}
        def ft(sql: str) -> List[sqlite3.Row]:
            return self._conn.execute(sql, (q, limit)).fetchall()
        res["characters"] = [self._row_to_character(r) for r in ft(
            "SELECT c.*, bm25(cf) as rank FROM characters_fts cf JOIN characters c ON c.id=cf.id WHERE cf MATCH ? ORDER BY rank LIMIT ?"
        )]
        res["events"] = [dict(r) for r in ft(
            "SELECT e.*, bm25(ef) as rank FROM events_fts ef JOIN events e ON e.id=ef.id WHERE ef MATCH ? ORDER BY rank LIMIT ?"
        )]
        res["world"] = [dict(r) for r in ft(
            "SELECT w.*, bm25(wf) as rank FROM world_fts wf JOIN world_entries w ON w.id=wf.id WHERE wf MATCH ? ORDER BY rank LIMIT ?"
        )]
        res["notes"] = [dict(r) for r in ft(
            "SELECT n.*, bm25(nf) as rank FROM notes_fts nf JOIN notes n ON n.id=nf.id WHERE nf MATCH ? ORDER BY rank LIMIT ?"
        )]
        return res

    # --- Consistency checks ---
    def consistency_checks(self) -> List[Dict[str, Any]]:
        issues: List[Dict[str, Any]] = []
        # duplicate character names
        dups = self._conn.execute(
            "SELECT name, COUNT(*) cnt FROM characters GROUP BY name HAVING cnt>1"
        ).fetchall()
        for r in dups:
            issues.append({"type": "DUP_CHARACTER_NAME", "name": r["name"], "count": r["cnt"]})
        # orphan relations
        orphans = self._conn.execute(
            """
            SELECT id FROM relations r
            WHERE NOT EXISTS(SELECT 1 FROM characters c WHERE c.id=r.source_id)
               OR NOT EXISTS(SELECT 1 FROM characters c WHERE c.id=r.target_id)
            """
        ).fetchall()
        for r in orphans:
            issues.append({"type": "ORPHAN_RELATION", "id": r["id"]})
        # event date contradictions
        bad = self._conn.execute(
            "SELECT id, start_date, end_date FROM events WHERE start_date IS NOT NULL AND end_date IS NOT NULL AND end_date < start_date"
        ).fetchall()
        for r in bad:
            issues.append({"type": "EVENT_DATE_ORDER", "id": r["id"], "start": r["start_date"], "end": r["end_date"]})
        return issues

